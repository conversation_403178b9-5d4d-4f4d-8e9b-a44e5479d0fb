#!/bin/bash
set -e

echo "Setting up CoachPad development environment..."

# Update system packages
sudo apt-get update

# Install required system packages
echo "Installing system dependencies..."
sudo apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    postgresql-client \
    podman \
    ca-certificates \
    gnupg \
    lsb-release

# Install Go 1.24.2
echo "Installing Go 1.24.2..."
if ! command -v go &> /dev/null || [[ "$(go version | cut -d' ' -f3)" != "go1.24.2" ]]; then
    wget -q https://go.dev/dl/go1.24.2.linux-amd64.tar.gz
    sudo rm -rf /usr/local/go
    sudo tar -C /usr/local -xzf go1.24.2.linux-amd64.tar.gz
    rm go1.24.2.linux-amd64.tar.gz
    
    # Add Go to PATH in profile
    echo 'export PATH=$PATH:/usr/local/go/bin' >> $HOME/.profile
    echo 'export GOPATH=$HOME/go' >> $HOME/.profile
    echo 'export PATH=$PATH:$GOPATH/bin' >> $HOME/.profile
    
    # Set for current session
    export PATH=$PATH:/usr/local/go/bin
    export GOPATH=$HOME/go
    export PATH=$PATH:$GOPATH/bin
fi

# Install Node.js 20.x
echo "Installing Node.js..."
if ! command -v node &> /dev/null || [[ "$(node --version | cut -d'.' -f1)" != "v20" ]]; then
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install additional Go tools
echo "Installing Go tools..."
go install golang.org/x/tools/cmd/goimports@latest
go install github.com/pressly/goose/v3/cmd/goose@latest
go install github.com/sqlc-dev/sqlc/cmd/sqlc@latest

# Add Go tools to PATH
echo 'export PATH=$PATH:$HOME/go/bin' >> $HOME/.profile

# Install Node.js dependencies
echo "Installing Node.js dependencies..."
npm install

# Install Playwright browsers
echo "Installing Playwright browsers..."
npx playwright install --with-deps

# Set up PostgreSQL container with Podman
echo "Setting up PostgreSQL database..."
# Make bootstrap script executable
chmod +x bootstrap-db-dev.sh setup-databases.sh

# Start PostgreSQL container and set up databases
./bootstrap-db-dev.sh

# Wait a bit more for database to be fully ready
sleep 5

# Set up environment variables for development
echo "Setting up environment variables..."
cat > .env.development << 'EOF'
# Database
DATABASE_URL=postgresql://coachpad@localhost:5432/coachpad_development

# Server
COACHPAD_BACKEND_PORT=8000
APP_ENV=development

# Required service keys (using dummy values for testing)
STRIPE_PUBLISHABLE_KEY=pk_test_dummy
STRIPE_PRO_PLAN_PRICE_ID=price_dummy
STRIPE_WEBHOOK_SECRET=whsec_dummy
STYTCH_PROJECT_ID=project-test-dummy
STYTCH_SECRET=secret-test-dummy
MAILGUN_API_KEY=key-dummy
MAILGUN_DOMAIN=sandbox.mailgun.org
EMAIL_CONFIRMATION_ENABLED=false
MAILGUN_CONFIRMATION_TEMPLATE_EN=dummy
BASE_URL=http://localhost:8000
EOF

# Run database migrations
echo "Running database migrations..."
export DATABASE_URL=postgresql://coachpad@localhost:5432/coachpad_development
goose -dir migrations postgres "$DATABASE_URL" up

# Generate SQLC code
echo "Generating SQLC code..."
sqlc generate

# Build frontend assets
echo "Building frontend assets..."
npx vite build

# Verify Go dependencies
echo "Verifying Go dependencies..."
go mod tidy
go mod download

echo "Setup completed successfully!"
echo "Environment is ready for testing."